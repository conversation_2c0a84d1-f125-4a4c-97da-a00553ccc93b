import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;
import 'image_compression_service.dart';

class ImagePickerService {
  static final ImagePicker _picker = ImagePicker();

  // Maximum file size in bytes (5MB)
  static const int maxFileSize = 5 * 1024 * 1024;

  // Allowed image extensions
  static const List<String> allowedExtensions = ['.jpg', '.jpeg', '.png'];

  /// Pick multiple images from gallery with compression
  static Future<List<XFile>?> pickMultipleImages({
    int? imageQuality = 60, // Reduced default quality for better compression
    int? maxImages,
    bool enableCompression = true,
  }) async {
    try {
      final List<XFile> images = await _picker.pickMultiImage(
        imageQuality: imageQuality,
      );

      if (images.isEmpty) return null;

      // Limit number of images if specified
      List<XFile> selectedImages = images;
      if (maxImages != null && images.length > maxImages) {
        selectedImages = images.take(maxImages).toList();
      }

      // Validate and compress each image
      List<XFile> processedImages = [];
      for (XFile image in selectedImages) {
        if (await _validateImage(image)) {
          if (enableCompression) {
            final compressedFile = await _compressImage(File(image.path));
            if (compressedFile != null) {
              processedImages.add(XFile(compressedFile.path));
            } else {
              processedImages.add(image); // Use original if compression fails
            }
          } else {
            processedImages.add(image);
          }
        }
      }

      return processedImages.isNotEmpty ? processedImages : null;
    } catch (e) {
      throw ImagePickerException('Error picking images: $e');
    }
  }

  /// Pick single image from gallery with compression
  static Future<XFile?> pickSingleImage({
    int? imageQuality = 60, // Reduced default quality
    bool enableCompression = true,
  }) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: imageQuality,
      );

      if (image == null) return null;

      if (await _validateImage(image)) {
        if (enableCompression) {
          final compressedFile = await _compressImage(File(image.path));
          if (compressedFile != null) {
            return XFile(compressedFile.path);
          }
        }
        return image;
      } else {
        return null;
      }
    } catch (e) {
      throw ImagePickerException('Error picking image: $e');
    }
  }

  /// Pick image from camera with compression
  static Future<XFile?> pickImageFromCamera({
    int? imageQuality = 60, // Reduced default quality
    bool enableCompression = true,
  }) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: imageQuality,
      );

      if (image == null) return null;

      if (await _validateImage(image)) {
        if (enableCompression) {
          final compressedFile = await _compressImage(File(image.path));
          if (compressedFile != null) {
            return XFile(compressedFile.path);
          }
        }
        return image;
      } else {
        return null;
      }
    } catch (e) {
      throw ImagePickerException('Error taking photo: $e');
    }
  }

  /// Validate image file
  static Future<bool> _validateImage(XFile image) async {
    try {
      // Check file extension
      String extension = path.extension(image.path).toLowerCase();
      if (!allowedExtensions.contains(extension)) {
        throw ImagePickerException(
          'Format file tidak didukung. Gunakan JPG, JPEG, atau PNG.',
        );
      }

      // Check file size
      final File file = File(image.path);
      final int fileSize = await file.length();
      if (fileSize > maxFileSize) {
        throw ImagePickerException('Ukuran file terlalu besar. Maksimal 5MB.');
      }

      return true;
    } catch (e) {
      if (e is ImagePickerException) {
        rethrow;
      }
      throw ImagePickerException('Error validating image: $e');
    }
  }

  /// Get file size in human readable format
  static String getFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  /// Show image picker options dialog
  static Future<XFile?> showImagePickerDialog(BuildContext context) async {
    return showDialog<XFile?>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Pilih Gambar'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Galeri'),
                onTap: () async {
                  Navigator.pop(context);
                  final image = await pickSingleImage();
                  if (context.mounted) {
                    Navigator.pop(context, image);
                  }
                },
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text('Kamera'),
                onTap: () async {
                  Navigator.pop(context);
                  final image = await pickImageFromCamera();
                  if (context.mounted) {
                    Navigator.pop(context, image);
                  }
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Batal'),
            ),
          ],
        );
      },
    );
  }

  /// Build image preview widget
  static Widget buildImagePreview({
    required XFile image,
    required VoidCallback onRemove,
    double? width = 100,
    double? height = 100,
  }) {
    return Container(
      width: width,
      height: height,
      margin: const EdgeInsets.only(right: 8),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(6)),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(6),
            child: Image.file(
              File(image.path),
              fit: BoxFit.cover,
              width: double.infinity,
              height: double.infinity,
            ),
          ),
          Positioned(
            top: 4,
            right: 4,
            child: GestureDetector(
              onTap: onRemove,
              child: Container(
                width: 20,
                height: 20,
                decoration: const BoxDecoration(
                  color: Colors.black54,
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.close, color: Colors.white, size: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build add image button widget
  static Widget buildAddImageButton({
    required VoidCallback onTap,
    double? width = 100,
    double? height = 100,
    String text = 'Tambah',
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: width,
        height: height,
        margin: const EdgeInsets.only(right: 8),
        decoration: BoxDecoration(
          color: const Color(0xFFF8F9FA),
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: const Color(0xFFE9ECEF),
            style: BorderStyle.solid,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.add, size: 24, color: Colors.grey[400]),
            const SizedBox(height: 4),
            Text(text, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
          ],
        ),
      ),
    );
  }

  /// Compress image using ImageCompressionService
  static Future<File?> _compressImage(File imageFile) async {
    try {
      return await ImageCompressionService.compressImage(imageFile);
    } catch (e) {
      // If compression fails, return null so original image is used
      return null;
    }
  }
}

/// Custom exception for image picker errors
class ImagePickerException implements Exception {
  final String message;

  const ImagePickerException(this.message);

  @override
  String toString() => message;
}
