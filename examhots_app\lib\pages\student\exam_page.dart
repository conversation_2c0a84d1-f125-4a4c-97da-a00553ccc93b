import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io';
import 'dart:async';
import 'package:image_picker/image_picker.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';
import '../../models/exam.dart';
import '../../models/question.dart';
import '../../services/api_service.dart';
import '../../student_home_page.dart';
import '../../config/app_config.dart';

class ExamPage extends StatefulWidget {
  final Exam exam;

  const ExamPage({super.key, required this.exam});

  @override
  State<ExamPage> createState() => _ExamPageState();
}

class _ExamPageState extends State<ExamPage> with WidgetsBindingObserver {
  PageController _pageController = PageController();
  int _currentQuestionIndex = 0;
  Map<int, dynamic> _answers = {};
  Map<int, TextEditingController> _textControllers = {};
  List<Question> _questions = [];
  bool _isLoading = true;
  Timer? _examTimer;
  Duration _remainingTime = Duration.zero;
  bool _examEnded = false;
  bool _isSubmitting = false;
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _setExamInProgress();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadQuestionsAndAnswers();
      _startExamTimer();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _examTimer?.cancel();
    _pageController.dispose();
    // Dispose all text controllers
    for (var controller in _textControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  // Get or create TextEditingController for a question
  TextEditingController _getTextController(int questionId) {
    if (!_textControllers.containsKey(questionId)) {
      _textControllers[questionId] = TextEditingController();
    }
    return _textControllers[questionId]!;
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // Prevent user from leaving the app during exam
    if (state == AppLifecycleState.paused ||
        state == AppLifecycleState.detached) {
      // User tried to leave the app, but we'll bring them back to exam
      if (!_examEnded) {
        SystemNavigator.pop(); // This will minimize the app but not close it
      }
    }
  }

  // Set exam in progress flag to prevent navigation
  Future<void> _setExamInProgress() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('exam_in_progress', widget.exam.id);
    await prefs.setString('exam_start_time', DateTime.now().toIso8601String());
  }

  // Clear exam in progress flag
  Future<void> _clearExamInProgress() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('exam_in_progress');
    await prefs.remove('exam_start_time');
  }

  // Start exam timer
  void _startExamTimer() {
    final now = DateTime.now();
    final examEnd = DateTime.parse(
      '${widget.exam.enddate} ${widget.exam.endtime}',
    );

    // Calculate remaining time
    _remainingTime = examEnd.difference(now);

    if (_remainingTime.isNegative) {
      _endExamAutomatically();
      return;
    }

    _examTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _remainingTime = _remainingTime - const Duration(seconds: 1);

        if (_remainingTime.isNegative || _remainingTime.inSeconds <= 0) {
          _endExamAutomatically();
        }
      });
    });
  }

  // End exam automatically when time is up
  void _endExamAutomatically() {
    if (_examEnded) return;

    _examTimer?.cancel();
    setState(() {
      _examEnded = true;
    });

    _submitExamAutomatically();
  }

  // Load questions and then temporary answers
  Future<void> _loadQuestionsAndAnswers() async {
    try {
      print('Starting to load questions...');
      await _loadQuestions();
      print('Questions loaded, now loading temporary answers...');
      await _loadTemporaryAnswers();
      print('Temporary answers loaded successfully');
    } catch (e) {
      print('Error in _loadQuestionsAndAnswers: $e');
    }
  }

  Future<void> _loadQuestions() async {
    try {
      final response = await ApiService.getExamQuestions(widget.exam.id);

      if (response['success'] == true) {
        final questionsData = response['data']['questions'] as List<dynamic>;
        List<Question> allQuestions = [];

        // Parse questions from the response
        for (var questionJson in questionsData) {
          allQuestions.add(Question.fromJson(questionJson));
        }

        setState(() {
          _questions = allQuestions;
          _isLoading = false;
        });
      } else {
        throw Exception(response['message'] ?? 'Failed to load questions');
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading questions: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Load temporary answers from previous session
  Future<void> _loadTemporaryAnswers() async {
    try {
      print('Loading temporary answers for exam ${widget.exam.id}...');
      final response = await ApiService.getTemporaryAnswers(widget.exam.id);
      print('Temporary answers response: $response');

      if (response['success'] == true) {
        final List<dynamic> answersData = response['data']['answers'];
        print('Raw answers data: $answersData');

        // Convert temporary answers to local format
        Map<int, dynamic> tempAnswers = {};
        for (var answerData in answersData) {
          try {
            // Safely parse questionid as it might be string or int
            int questionId =
                answerData['questionid'] is int
                    ? answerData['questionid']
                    : int.parse(answerData['questionid'].toString());
            String questionType = answerData['question_type'];
            print(
              'Processing answer for question $questionId, type: $questionType, data: $answerData',
            );

            if (questionType == 'pilihan_ganda') {
              // Handle selected_answer which might be int or string
              final selectedAnswerRaw = answerData['selected_answer'];
              int selectedAnswer = 0;
              if (selectedAnswerRaw != null) {
                if (selectedAnswerRaw is int) {
                  selectedAnswer = selectedAnswerRaw;
                } else {
                  selectedAnswer =
                      int.tryParse(selectedAnswerRaw.toString()) ?? 0;
                }
              }

              if (selectedAnswer > 0) {
                tempAnswers[questionId] = selectedAnswer;
                print(
                  'Set pilihan_ganda answer for question $questionId: $selectedAnswer',
                );
              }
            } else if (questionType == 'uraian_singkat') {
              final textAnswer = answerData['text_answer']?.toString() ?? '';
              if (textAnswer.isNotEmpty) {
                tempAnswers[questionId] = textAnswer;
                print(
                  'Set uraian_singkat answer for question $questionId: $textAnswer',
                );
              }
            } else if (questionType == 'esai') {
              final textAnswer = answerData['text_answer']?.toString() ?? '';
              tempAnswers[questionId] = {
                'text': textAnswer,
                'images':
                    <File>[], // Images will be loaded separately if needed
              };
              print('Set esai answer for question $questionId: $textAnswer');
            }
          } catch (e) {
            print('Error processing answer data: $answerData, error: $e');
          }
        }

        setState(() {
          _answers = tempAnswers;
        });

        // Initialize text controllers with existing answers
        for (var entry in tempAnswers.entries) {
          int questionId = entry.key;
          var answer = entry.value;
          print(
            'Initializing text controller for question $questionId with answer: $answer',
          );

          if (answer is String && answer.isNotEmpty) {
            // For uraian_singkat
            _getTextController(questionId).text = answer;
            print(
              'Set text controller for uraian_singkat question $questionId: $answer',
            );
          } else if (answer is Map && answer['text'] != null) {
            // For esai
            _getTextController(questionId).text = answer['text'];
            print(
              'Set text controller for esai question $questionId: ${answer['text']}',
            );
          }
        }

        print('Loaded ${tempAnswers.length} temporary answers: $tempAnswers');
      }
    } catch (e) {
      // It's normal if no temporary answers exist
      print('No temporary answers found: $e');
    }
  }

  void _saveAnswer(int questionId, dynamic answer) {
    print('Saving answer for question $questionId: $answer');
    setState(() {
      _answers[questionId] = answer;
    });

    // Auto-save to temporary answers
    _saveTemporaryAnswer(questionId, answer);
  }

  // Save answer to temporary storage
  Future<void> _saveTemporaryAnswer(int questionId, dynamic answer) async {
    try {
      // Find the question to get its type
      final question = _questions.firstWhere((q) => q.id == questionId);

      String? selectedAnswer;
      String? textAnswer;
      String? imagePath;
      List<String>? imagePaths;
      bool hasAttachment = false;

      if (question.type == 'pilihan_ganda') {
        selectedAnswer = answer?.toString();
      } else if (question.type == 'uraian_singkat') {
        textAnswer = answer?.toString();
      } else if (question.type == 'esai') {
        if (answer is Map) {
          textAnswer = answer['text']?.toString();
          if (answer['images'] is List &&
              (answer['images'] as List).isNotEmpty) {
            List<File> images = answer['images'] as List<File>;
            imagePath =
                images.first.path; // Keep first for backward compatibility
            imagePaths =
                images.map((img) => img.path).toList(); // Store all paths
            hasAttachment = true;
          }
        } else {
          textAnswer = answer?.toString();
        }
      }

      await ApiService.saveTemporaryAnswer(
        examId: widget.exam.id,
        questionId: questionId,
        questionType: question.type,
        selectedAnswer: selectedAnswer,
        textAnswer: textAnswer,
        imagePath: imagePath,
        imagePaths: imagePaths,
        // Send multiple image paths
        hasAttachment: hasAttachment,
      );
    } catch (e) {
      // Don't show error to user for temporary save failures
      // Silently fail for temporary saves
    }
  }

  // Calculate exam progress
  double get _examProgress {
    if (_questions.isEmpty) return 0.0;
    int answeredQuestions = 0;

    for (var question in _questions) {
      if (_answers.containsKey(question.id)) {
        var answer = _answers[question.id];
        if (answer != null) {
          if (question.type == 'pilihan_ganda' && answer != 0) {
            answeredQuestions++;
          } else if (question.type == 'uraian_singkat' &&
              answer.toString().isNotEmpty) {
            answeredQuestions++;
          } else if (question.type == 'esai') {
            if (answer is Map) {
              // Check if either text or images are provided
              bool hasText = answer['text']?.toString().isNotEmpty == true;
              bool hasImages =
                  answer['images'] is List &&
                  (answer['images'] as List).isNotEmpty;
              if (hasText || hasImages) {
                answeredQuestions++;
              }
            } else if (answer is String && answer.isNotEmpty) {
              answeredQuestions++;
            }
          }
        }
      }
    }

    return answeredQuestions / _questions.length;
  }

  String get _progressText {
    int answeredQuestions = (_examProgress * _questions.length).round();
    return '$answeredQuestions/${_questions.length} soal dijawab';
  }

  void _nextQuestion() {
    if (_currentQuestionIndex < _questions.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousQuestion() {
    if (_currentQuestionIndex > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  // Submit exam automatically when time is up
  Future<void> _submitExamAutomatically() async {
    await _submitExam(isAutomatic: true);
  }

  // Submit exam manually by user
  void _handleSubmitExam() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: const Text('Konfirmasi'),
            content: const Text('Apakah Anda yakin ingin mengumpulkan ujian?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Batal'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _submitExam(isAutomatic: false);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF455A9D),
                ),
                child: const Text(
                  'Ya, Kumpulkan',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
    );
  }

  // Submit exam to API
  Future<void> _submitExam({required bool isAutomatic}) async {
    if (_isSubmitting) return;

    setState(() {
      _isSubmitting = true;
      _examEnded = true;
    });

    try {
      // Prepare answers for submission
      List<Map<String, dynamic>> submissionData = [];

      for (var question in _questions) {
        final answer = _answers[question.id];

        Map<String, dynamic> answerData = {
          'question_id': question.id,
          'question_type': question.type,
        };

        if (question.type == 'pilihan_ganda') {
          answerData['selected_answer'] = answer?.toString() ?? '';
          answerData['text_answer'] = null;
          answerData['image_path'] = null;
          answerData['image_paths'] = null;
          answerData['has_attachment'] = null;
        } else if (question.type == 'uraian_singkat') {
          answerData['selected_answer'] = null;
          answerData['text_answer'] = answer?.toString() ?? '';
          answerData['image_path'] = null;
          answerData['image_paths'] = null;
          answerData['has_attachment'] = null;
        } else if (question.type == 'esai') {
          answerData['selected_answer'] = null;
          if (answer is Map) {
            answerData['text_answer'] = answer['text']?.toString() ?? '';
            // Handle images/files - upload all images if exists
            if (answer['images'] is List &&
                (answer['images'] as List).isNotEmpty) {
              List<File> images = answer['images'] as List<File>;
              try {
                if (images.length == 1) {
                  // Single image - use existing endpoint for backward compatibility
                  final uploadResult = await ApiService.uploadAnswerImage(
                    images.first.path,
                  );
                  if (uploadResult['success'] == true) {
                    answerData['image_path'] = uploadResult['data']['filename'];
                    answerData['image_paths'] = [
                      uploadResult['data']['filename'],
                    ];
                    answerData['has_attachment'] = true;
                  } else {
                    answerData['image_path'] = null;
                    answerData['image_paths'] = null;
                    answerData['has_attachment'] = false;
                  }
                } else {
                  // Multiple images - use new endpoint
                  final imagePaths = images.map((img) => img.path).toList();
                  final uploadResult = await ApiService.uploadAnswerImages(
                    imagePaths,
                  );
                  if (uploadResult['success'] == true) {
                    final uploadedFiles = uploadResult['data']['files'] as List;
                    final filenames =
                        uploadedFiles
                            .map((file) => file['filename'] as String)
                            .toList();
                    answerData['image_path'] =
                        filenames
                            .first; // Keep first for backward compatibility
                    answerData['image_paths'] = filenames;
                    answerData['has_attachment'] = true;
                  } else {
                    answerData['image_path'] = null;
                    answerData['image_paths'] = null;
                    answerData['has_attachment'] = false;
                  }
                }
              } catch (e) {
                // Failed to upload images, continue without attachment
                answerData['image_path'] = null;
                answerData['image_paths'] = null;
                answerData['has_attachment'] = false;
              }
            } else {
              answerData['image_path'] = null;
              answerData['image_paths'] = null;
              answerData['has_attachment'] = false;
            }
          } else {
            answerData['text_answer'] = answer?.toString() ?? '';
            answerData['image_path'] = null;
            answerData['image_paths'] = null;
            answerData['has_attachment'] = false;
          }
        }

        submissionData.add(answerData);
      }

      // Submit to API
      final response = await ApiService.submitExamAnswers(
        widget.exam.id,
        submissionData,
      );

      if (response['success'] == true) {
        await _clearExamInProgress();

        if (mounted) {
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => const StudentHomePage()),
            (route) => false,
          );

          // Check if exam contains essay questions
          bool hasEssayQuestions = _questions.any(
            (question) => question.type == 'esai',
          );

          String message;
          if (isAutomatic) {
            message =
                hasEssayQuestions
                    ? 'Waktu ujian habis! Jawaban telah dikumpulkan otomatis. Skor sementara akan ditampilkan karena terdapat soal esai.'
                    : 'Waktu ujian habis! Jawaban telah dikumpulkan otomatis.';
          } else {
            message =
                hasEssayQuestions
                    ? 'Ujian berhasil diselesaikan! Skor yang ditampilkan bersifat sementara karena terdapat soal esai yang akan diperiksa manual oleh guru.'
                    : 'Ujian berhasil diselesaikan!';
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: Colors.green,
              duration: Duration(seconds: hasEssayQuestions ? 5 : 3),
            ),
          );
        }
      } else {
        throw Exception(response['message'] ?? 'Failed to submit exam');
      }
    } catch (e) {
      setState(() {
        _isSubmitting = false;
        _examEnded = false;
      });

      if (mounted) {
        String errorMessage = e.toString().replaceAll('Exception: ', '');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error submitting exam: $errorMessage'),
            backgroundColor: Colors.red,
            duration: const Duration(
              seconds: 5,
            ), // Show longer for validation errors
          ),
        );
      }
    }
  }

  // Format time duration to HH:MM:SS
  String _formatTime(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String hours = twoDigits(duration.inHours);
    String minutes = twoDigits(duration.inMinutes.remainder(60));
    String seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$hours:$minutes:$seconds';
  }

  // Show question navigation menu
  void _showQuestionNavigation() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            height: MediaQuery.of(context).size.height * 0.7,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              children: [
                // Header
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: const BoxDecoration(
                    color: Color(0xFF455A9D),
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(20),
                    ),
                  ),
                  child: Row(
                    children: [
                      const Expanded(
                        child: Text(
                          'Navigasi Soal',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.close, color: Colors.white),
                      ),
                    ],
                  ),
                ),
                // Question Grid
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: GridView.builder(
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 5,
                            crossAxisSpacing: 8,
                            mainAxisSpacing: 8,
                            childAspectRatio: 1,
                          ),
                      itemCount: _questions.length,
                      itemBuilder: (context, index) {
                        final question = _questions[index];
                        final isAnswered = _isQuestionAnswered(question);
                        final isCurrent = index == _currentQuestionIndex;

                        return GestureDetector(
                          onTap: () {
                            Navigator.pop(context);
                            _pageController.animateToPage(
                              index,
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                            );
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              color:
                                  isCurrent
                                      ? const Color(0xFF455A9D)
                                      : isAnswered
                                      ? Colors.green
                                      : Colors.grey[300],
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color:
                                    isCurrent
                                        ? Colors.white
                                        : Colors.transparent,
                                width: 2,
                              ),
                            ),
                            child: Center(
                              child: Text(
                                '${index + 1}',
                                style: TextStyle(
                                  color:
                                      isCurrent || isAnswered
                                          ? Colors.white
                                          : Colors.black,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
                // Legend
                Container(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildLegendItem(const Color(0xFF455A9D), 'Saat ini'),
                      _buildLegendItem(Colors.green, 'Sudah dijawab'),
                      _buildLegendItem(Colors.grey[300]!, 'Belum dijawab'),
                    ],
                  ),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildLegendItem(Color color, String label) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(width: 4),
        Text(label, style: const TextStyle(fontSize: 12)),
      ],
    );
  }

  void _saveEssayAnswer(int questionId, String text, List<File> images) {
    _saveAnswer(questionId, {'text': text, 'images': images});
  }

  // Check if a question is answered (considering both text and images for essay)
  bool _isQuestionAnswered(Question question) {
    if (!_answers.containsKey(question.id)) return false;

    var answer = _answers[question.id];
    if (answer == null) return false;

    if (question.type == 'pilihan_ganda') {
      return answer != 0;
    } else if (question.type == 'uraian_singkat') {
      return answer.toString().isNotEmpty;
    } else if (question.type == 'esai') {
      if (answer is Map) {
        // Check if either text or images are provided
        bool hasText = answer['text']?.toString().isNotEmpty == true;
        bool hasImages =
            answer['images'] is List && (answer['images'] as List).isNotEmpty;
        return hasText || hasImages;
      } else if (answer is String) {
        return answer.isNotEmpty;
      }
    }

    return false;
  }

  // Pick images using same implementation as bank soal
  Future<void> _pickImages(int questionId, List<File> currentImages) async {
    try {
      final List<XFile> images = await _picker.pickMultiImage(imageQuality: 80);
      if (images.isNotEmpty) {
        final List<File> updatedImages = List<File>.from(currentImages);
        for (XFile image in images) {
          updatedImages.add(File(image.path));
        }

        // Get current text from controller
        final controller = _getTextController(questionId);
        final currentText = controller.text;

        // Update the answer with new images
        _saveEssayAnswer(questionId, currentText, updatedImages);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Gambar berhasil ditambahkan'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error memilih gambar: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Camera picker with proper permission handling
  Future<void> _pickImageFromCamera(
    int questionId,
    List<File> currentImages,
  ) async {
    try {
      // Use camera picker
      final AssetEntity? entity = await CameraPicker.pickFromCamera(
        context,
        locale: const Locale('en'),
        pickerConfig: CameraPickerConfig(
          permissionRequestOption: PermissionRequestOption(
            androidPermission: AndroidPermission(
              type: RequestType.image,
              mediaLocation: true,
            ),
          ),
        ),
      );

      if (entity != null) {
        // Convert AssetEntity to File
        final File? file = await entity.file;
        if (file != null) {
          final List<File> updatedImages = List<File>.from(currentImages);
          updatedImages.add(file);

          // Get current text from controller
          final controller = _getTextController(questionId);
          final currentText = controller.text;

          // Update the answer with new images
          _saveEssayAnswer(questionId, currentText, updatedImages);

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Foto berhasil diambil'),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 2),
              ),
            );
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error mengambil foto: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Remove image using same implementation as bank soal
  void _removeImageFromEssay(
    int questionId,
    int index,
    List<File> currentImages,
  ) {
    final List<File> updatedImages = List<File>.from(currentImages);
    updatedImages.removeAt(index);

    // Get current text from controller
    final controller = _getTextController(questionId);
    final currentText = controller.text;

    // Update the answer with removed image
    _saveEssayAnswer(questionId, currentText, updatedImages);

    if (mounted) {
      setState(() {
        // Trigger rebuild to update UI
      });
    }
  }

  List<Widget> _buildQuestionImages(String imgString) {
    // Split by comma to handle multiple images
    final images =
        imgString.split(',').where((img) => img.trim().isNotEmpty).toList();

    return images.map((image) {
      final imageUrl = AppConfig.getQuestionImageUrl(image.trim());

      return Container(
        margin: const EdgeInsets.only(bottom: 8),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.network(
            imageUrl,
            width: double.infinity,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                height: 100,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.broken_image, color: Colors.grey, size: 32),
                      SizedBox(height: 4),
                      Text(
                        'Gambar tidak dapat dimuat',
                        style: TextStyle(color: Colors.grey, fontSize: 12),
                      ),
                    ],
                  ),
                ),
              );
            },
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Container(
                height: 150,
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(
                  child: CircularProgressIndicator(color: Color(0xFF455A9D)),
                ),
              );
            },
          ),
        ),
      );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FD),
      appBar: AppBar(
        backgroundColor: const Color(0xFF455A9D),
        elevation: 0,
        automaticallyImplyLeading: false,
        // Disable back button
        title: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    widget.exam.name,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    _progressText,
                    style: const TextStyle(color: Colors.white70, fontSize: 12),
                  ),
                ],
              ),
            ),
            // Progress indicator
            Container(
              margin: const EdgeInsets.only(right: 12),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    '${(_examProgress * 100).round()}%',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(2),
                    ),
                    child: FractionallySizedBox(
                      alignment: Alignment.centerLeft,
                      widthFactor: _examProgress,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Countdown Timer
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color:
                    _remainingTime.inMinutes <= 5
                        ? Colors.red
                        : Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                _formatTime(_remainingTime),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        actions: [
          // Question Navigation Menu
          IconButton(
            icon: const Icon(Icons.menu, color: Colors.white),
            onPressed: _showQuestionNavigation,
          ),
          // Submit Button
          IconButton(
            icon: const Icon(Icons.send, color: Colors.white),
            onPressed: _examEnded ? null : _handleSubmitExam,
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(
                child: CircularProgressIndicator(color: Color(0xFF455A9D)),
              )
              : _questions.isEmpty
              ? const Center(
                child: Text(
                  'Tidak ada soal untuk ujian ini',
                  style: TextStyle(fontSize: 16),
                ),
              )
              : Column(
                children: [
                  // Progress indicator
                  Container(
                    padding: const EdgeInsets.all(16),
                    color: Colors.white,
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Soal ${_currentQuestionIndex + 1} dari ${_questions.length}',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              '${_questions.where(_isQuestionAnswered).length}/${_questions.length} dijawab',
                              style: const TextStyle(
                                fontSize: 14,
                                color: Color(0xFF666666),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        LinearProgressIndicator(
                          value:
                              (_currentQuestionIndex + 1) / _questions.length,
                          backgroundColor: Colors.grey[300],
                          valueColor: const AlwaysStoppedAnimation<Color>(
                            Color(0xFF455A9D),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Questions
                  Expanded(
                    child: PageView.builder(
                      controller: _pageController,
                      onPageChanged: (index) {
                        setState(() {
                          _currentQuestionIndex = index;
                        });
                      },
                      itemCount: _questions.length,
                      itemBuilder: (context, index) {
                        final question = _questions[index];
                        return _buildQuestionWidget(question);
                      },
                    ),
                  ),

                  // Navigation buttons
                  Container(
                    padding: const EdgeInsets.all(16),
                    color: Colors.white,
                    child: Row(
                      children: [
                        if (_currentQuestionIndex > 0)
                          Expanded(
                            child: OutlinedButton(
                              onPressed: _previousQuestion,
                              style: OutlinedButton.styleFrom(
                                side: const BorderSide(
                                  color: Color(0xFF455A9D),
                                ),
                              ),
                              child: const Text(
                                'Sebelumnya',
                                style: TextStyle(color: Color(0xFF455A9D)),
                              ),
                            ),
                          ),
                        if (_currentQuestionIndex > 0)
                          const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed:
                                _examEnded
                                    ? null
                                    : (_currentQuestionIndex <
                                            _questions.length - 1
                                        ? _nextQuestion
                                        : _handleSubmitExam),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF455A9D),
                              foregroundColor: Colors.white,
                            ),
                            child: Text(
                              _currentQuestionIndex < _questions.length - 1
                                  ? 'Selanjutnya'
                                  : 'Submit Ujian',
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
    );
  }

  Widget _buildQuestionWidget(Question question) {
    switch (question.type) {
      case 'pilihan_ganda':
        return _buildMultipleChoiceQuestion(question);
      case 'uraian_singkat':
        return _buildShortAnswerQuestion(question);
      case 'esai':
        return _buildEssayQuestion(question);
      default:
        return const Center(child: Text('Tipe soal tidak dikenal'));
    }
  }

  Widget _buildMultipleChoiceQuestion(Question question) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Question text
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Pilihan Ganda',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  question.question,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),

                // Question images if exist
                if (question.img != null && question.img!.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  ..._buildQuestionImages(question.img!),
                ],
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Answer options
          ...question.answers.map((answer) {
            final isSelected = _answers[question.id] == answer.id;
            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              child: InkWell(
                onTap: () => _saveAnswer(question.id, answer.id),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color:
                        isSelected
                            ? const Color(0xFF455A9D).withValues(alpha: 0.1)
                            : Colors.white,
                    border: Border.all(
                      color:
                          isSelected
                              ? const Color(0xFF455A9D)
                              : Colors.grey[300]!,
                      width: isSelected ? 2 : 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color:
                                isSelected
                                    ? const Color(0xFF455A9D)
                                    : Colors.grey[400]!,
                            width: 2,
                          ),
                          color:
                              isSelected
                                  ? const Color(0xFF455A9D)
                                  : Colors.transparent,
                        ),
                        child:
                            isSelected
                                ? const Icon(
                                  Icons.check,
                                  size: 14,
                                  color: Colors.white,
                                )
                                : null,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          answer.answer,
                          style: TextStyle(
                            fontSize: 14,
                            color:
                                isSelected
                                    ? const Color(0xFF455A9D)
                                    : Colors.black87,
                            fontWeight:
                                isSelected
                                    ? FontWeight.w500
                                    : FontWeight.normal,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildShortAnswerQuestion(Question question) {
    final TextEditingController controller = _getTextController(question.id);

    // Update controller text if answer exists but controller is empty
    if (_answers[question.id] != null && controller.text.isEmpty) {
      controller.text = _answers[question.id];
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Question text
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Uraian Singkat',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  question.question,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),

                // Question images if exist
                if (question.img != null && question.img!.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  ..._buildQuestionImages(question.img!),
                ],
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Answer input
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Jawaban Anda:',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: controller,
                  maxLines: 4,
                  decoration: const InputDecoration(
                    hintText: 'Tulis jawaban singkat Anda di sini...',
                    border: OutlineInputBorder(),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Color(0xFF455A9D)),
                    ),
                  ),
                  onChanged: (value) {
                    _saveAnswer(question.id, value);
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEssayQuestion(Question question) {
    final TextEditingController controller = _getTextController(question.id);
    List<File> selectedImages = [];

    if (_answers[question.id] != null && _answers[question.id] is Map) {
      // Update controller text if answer exists but controller is empty
      if (controller.text.isEmpty) {
        controller.text = _answers[question.id]['text'] ?? '';
      }
      // Create a copy of the images list to avoid modifying the original
      final existingImages = _answers[question.id]['images'];
      if (existingImages is List<File>) {
        selectedImages = List<File>.from(existingImages);
      }
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Question text
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Esai',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  question.question,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),

                // Question images if exist
                if (question.img != null && question.img!.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  ..._buildQuestionImages(question.img!),
                ],
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Answer input
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Jawaban Anda:',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: controller,
                  maxLines: 8,
                  decoration: const InputDecoration(
                    hintText: 'Tulis jawaban esai Anda di sini...',
                    border: OutlineInputBorder(),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Color(0xFF455A9D)),
                    ),
                  ),
                  onChanged: (value) {
                    _saveEssayAnswer(question.id, value, selectedImages);
                  },
                ),

                const SizedBox(height: 16),

                // Image upload section
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed:
                            () => _pickImages(question.id, selectedImages),
                        icon: const Icon(Icons.photo_library),
                        label: const Text('Pilih Gambar'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: const Color(0xFF455A9D),
                          side: const BorderSide(color: Color(0xFF455A9D)),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed:
                            () => _pickImageFromCamera(
                              question.id,
                              selectedImages,
                            ),
                        icon: const Icon(Icons.camera_alt),
                        label: const Text('Ambil Foto'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: const Color(0xFF455A9D),
                          side: const BorderSide(color: Color(0xFF455A9D)),
                        ),
                      ),
                    ),
                  ],
                ),

                // Display selected images
                if (selectedImages.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  const Text(
                    'Gambar yang dipilih:',
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children:
                        selectedImages.asMap().entries.map((entry) {
                          int index = entry.key;
                          File image = entry.value;
                          return Stack(
                            children: [
                              Container(
                                width: 80,
                                height: 80,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  image: DecorationImage(
                                    image: FileImage(image),
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              Positioned(
                                top: 4,
                                right: 4,
                                child: GestureDetector(
                                  onTap:
                                      () => _removeImageFromEssay(
                                        question.id,
                                        index,
                                        selectedImages,
                                      ),
                                  child: Container(
                                    width: 20,
                                    height: 20,
                                    decoration: const BoxDecoration(
                                      color: Colors.red,
                                      shape: BoxShape.circle,
                                    ),
                                    child: const Icon(
                                      Icons.close,
                                      size: 14,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          );
                        }).toList(),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
