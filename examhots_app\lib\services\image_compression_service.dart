import 'dart:io';
import 'dart:typed_data';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

class ImageCompressionService {
  // Default compression settings
  static const int defaultQuality = 70; // 70% quality
  static const int maxWidth = 1920; // Max width in pixels
  static const int maxHeight = 1080; // Max height in pixels
  static const int maxFileSizeKB = 500; // Max file size in KB

  /// Compress a single image file
  static Future<File?> compressImage(
    File imageFile, {
    int quality = defaultQuality,
    int? maxWidth,
    int? maxHeight,
    int? maxFileSizeKB,
  }) async {
    try {
      // Get temporary directory
      final tempDir = await getTemporaryDirectory();
      final fileName = path.basenameWithoutExtension(imageFile.path);
      final extension = path.extension(imageFile.path).toLowerCase();
      
      // Generate unique filename
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final compressedPath = path.join(
        tempDir.path,
        '${fileName}_compressed_$timestamp$extension',
      );

      // Compress the image
      final compressedFile = await FlutterImageCompress.compressAndGetFile(
        imageFile.absolute.path,
        compressedPath,
        quality: quality,
        minWidth: maxWidth ?? ImageCompressionService.maxWidth,
        minHeight: maxHeight ?? ImageCompressionService.maxHeight,
        format: _getCompressFormat(extension),
      );

      if (compressedFile == null) {
        return imageFile; // Return original if compression fails
      }

      // Check if file size is within limit
      final compressedSize = await compressedFile.length();
      final targetSize = (maxFileSizeKB ?? ImageCompressionService.maxFileSizeKB) * 1024;

      if (compressedSize <= targetSize) {
        return File(compressedFile.path);
      }

      // If still too large, compress more aggressively
      if (quality > 30) {
        return await compressImage(
          imageFile,
          quality: quality - 20,
          maxWidth: maxWidth,
          maxHeight: maxHeight,
          maxFileSizeKB: maxFileSizeKB,
        );
      }

      // Return compressed file even if it's still large
      return File(compressedFile.path);
    } catch (e) {
      // If compression fails, return original file
      return imageFile;
    }
  }

  /// Compress multiple image files
  static Future<List<File>> compressImages(
    List<File> imageFiles, {
    int quality = defaultQuality,
    int? maxWidth,
    int? maxHeight,
    int? maxFileSizeKB,
  }) async {
    List<File> compressedFiles = [];

    for (File imageFile in imageFiles) {
      final compressedFile = await compressImage(
        imageFile,
        quality: quality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
        maxFileSizeKB: maxFileSizeKB,
      );

      if (compressedFile != null) {
        compressedFiles.add(compressedFile);
      }
    }

    return compressedFiles;
  }

  /// Compress image from bytes
  static Future<Uint8List?> compressImageFromBytes(
    Uint8List imageBytes, {
    int quality = defaultQuality,
    int? maxWidth,
    int? maxHeight,
    CompressFormat format = CompressFormat.jpeg,
  }) async {
    try {
      final compressedBytes = await FlutterImageCompress.compressWithList(
        imageBytes,
        quality: quality,
        minWidth: maxWidth ?? ImageCompressionService.maxWidth,
        minHeight: maxHeight ?? ImageCompressionService.maxHeight,
        format: format,
      );

      return compressedBytes;
    } catch (e) {
      return imageBytes; // Return original if compression fails
    }
  }

  /// Get compression format based on file extension
  static CompressFormat _getCompressFormat(String extension) {
    switch (extension.toLowerCase()) {
      case '.png':
        return CompressFormat.png;
      case '.webp':
        return CompressFormat.webp;
      case '.heic':
        return CompressFormat.heic;
      default:
        return CompressFormat.jpeg;
    }
  }

  /// Get file size in KB
  static Future<double> getFileSizeKB(File file) async {
    final bytes = await file.length();
    return bytes / 1024;
  }

  /// Get file size in MB
  static Future<double> getFileSizeMB(File file) async {
    final kb = await getFileSizeKB(file);
    return kb / 1024;
  }

  /// Check if image needs compression
  static Future<bool> needsCompression(
    File imageFile, {
    int maxFileSizeKB = maxFileSizeKB,
  }) async {
    final fileSizeKB = await getFileSizeKB(imageFile);
    return fileSizeKB > maxFileSizeKB;
  }

  /// Get compression info for debugging
  static Future<Map<String, dynamic>> getCompressionInfo(
    File originalFile,
    File compressedFile,
  ) async {
    final originalSize = await getFileSizeKB(originalFile);
    final compressedSize = await getFileSizeKB(compressedFile);
    final compressionRatio = ((originalSize - compressedSize) / originalSize) * 100;

    return {
      'original_size_kb': originalSize.toStringAsFixed(2),
      'compressed_size_kb': compressedSize.toStringAsFixed(2),
      'compression_ratio': '${compressionRatio.toStringAsFixed(1)}%',
      'size_reduction_kb': (originalSize - compressedSize).toStringAsFixed(2),
    };
  }
}
