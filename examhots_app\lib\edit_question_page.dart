import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:flutter/foundation.dart';
import 'dart:typed_data';
import 'models/question.dart';
import 'providers/question_provider.dart';

class EditQuestionPage extends StatefulWidget {
  final Question question;

  const EditQuestionPage({super.key, required this.question});

  @override
  State<EditQuestionPage> createState() => _EditQuestionPageState();
}

class _EditQuestionPageState extends State<EditQuestionPage> {
  final _questionController = TextEditingController();
  final List<TextEditingController> _answerControllers = [];
  final _scoreController = TextEditingController();
  final List<XFile> _selectedImages = [];
  final List<String> _existingImages = []; // For existing images from server
  final ImagePicker _picker = ImagePicker();

  int _correctAnswerIndex = 0;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    // Set question text
    _questionController.text = widget.question.question;

    // Initialize score for essay questions
    if (widget.question.type == 'esai' && widget.question.answers.isNotEmpty) {
      _scoreController.text =
          widget.question.answers[0].score?.toString() ?? '0';
    }

    // Initialize answer controllers based on question type
    if (widget.question.isPilihanGanda) {
      // For pilihan ganda, initialize 5 answer controllers (A, B, C, D, E)
      for (int i = 0; i < 5; i++) {
        _answerControllers.add(TextEditingController());
      }

      // Fill existing answers
      for (int i = 0; i < widget.question.answers.length && i < 5; i++) {
        _answerControllers[i].text = widget.question.answers[i].answer;
        if (widget.question.answers[i].isCorrect) {
          _correctAnswerIndex = i;
        }
      }
    } else {
      // For uraian singkat and esai, single answer
      _answerControllers.add(TextEditingController());
      if (widget.question.answers.isNotEmpty) {
        _answerControllers[0].text = widget.question.answers[0].answer;
      }
    }

    // Load existing images
    if (widget.question.img != null && widget.question.img!.isNotEmpty) {
      final imageNames = widget.question.img!.split(',');
      _existingImages.addAll(
        imageNames.where((name) => name.trim().isNotEmpty),
      );
    }
  }

  @override
  void dispose() {
    _questionController.dispose();
    _scoreController.dispose();
    for (var controller in _answerControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<void> _pickImages() async {
    try {
      final List<XFile> images = await _picker.pickMultiImage(imageQuality: 80);
      if (images.isNotEmpty) {
        setState(() {
          // Don't clear existing images, just add new ones like in create pages
          _selectedImages.addAll(images);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error picking images: $e')));
      }
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  Widget _buildImageWidget(XFile imageFile) {
    if (kIsWeb) {
      return FutureBuilder<Uint8List>(
        future: imageFile.readAsBytes(),
        builder: (context, snapshot) {
          if (snapshot.hasData) {
            return Image.memory(
              snapshot.data!,
              width: 100,
              height: 100,
              fit: BoxFit.cover,
            );
          } else {
            return Container(
              width: 100,
              height: 100,
              color: Colors.grey[300],
              child: const Center(child: CircularProgressIndicator()),
            );
          }
        },
      );
    } else {
      // For mobile platforms, use FutureBuilder to read file as bytes
      return FutureBuilder<Uint8List>(
        future: imageFile.readAsBytes(),
        builder: (context, snapshot) {
          if (snapshot.hasData) {
            return Image.memory(
              snapshot.data!,
              width: 100,
              height: 100,
              fit: BoxFit.cover,
            );
          } else if (snapshot.hasError) {
            return Container(
              width: 100,
              height: 100,
              color: Colors.grey[300],
              child: const Icon(Icons.error),
            );
          } else {
            return Container(
              width: 100,
              height: 100,
              color: Colors.grey[300],
              child: const Center(child: CircularProgressIndicator()),
            );
          }
        },
      );
    }
  }

  Widget _buildExistingImageWidget(String imageName) {
    return Image.network(
      'https://hots.karpeldevtech.cloud/storage/uploads/images/question/$imageName',
      width: 100,
      height: 100,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        return Container(
          width: 100,
          height: 100,
          color: Colors.grey[300],
          child: const Icon(Icons.error, color: Colors.red),
        );
      },
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return Container(
          width: 100,
          height: 100,
          child: Center(
            child: CircularProgressIndicator(
              value:
                  loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
            ),
          ),
        );
      },
    );
  }

  void _removeExistingImage(int index) {
    setState(() {
      _existingImages.removeAt(index);
    });
  }

  Future<void> _updateQuestion() async {
    if (_questionController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Pertanyaan harus diisi!'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Validate answers based on question type
    if (widget.question.isPilihanGanda) {
      // All 5 answers must be filled for multiple choice
      for (int i = 0; i < _answerControllers.length; i++) {
        if (_answerControllers[i].text.trim().isEmpty) {
          String letter = String.fromCharCode(65 + i); // A, B, C, D, E
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Jawaban $letter wajib diisi!'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }
      }
    } else {
      if (_answerControllers[0].text.trim().isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Jawaban harus diisi!'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // Validate score for essay questions
      if (widget.question.type == 'esai') {
        if (_scoreController.text.trim().isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Skor harus diisi!'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }

        final score = int.tryParse(_scoreController.text.trim());
        if (score == null || score < 0) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Skor harus berupa angka positif!'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }
      }
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final provider = context.read<QuestionProvider>();

      List<Map<String, String>>? answers;
      String? answer;
      double? score;

      if (widget.question.isPilihanGanda) {
        answers = [];
        for (var controller in _answerControllers) {
          // Add all answers, including empty ones to maintain index consistency
          answers.add({'text': controller.text.trim()});
        }
      } else {
        answer = _answerControllers[0].text.trim();

        // Get score for essay questions
        if (widget.question.type == 'esai' &&
            _scoreController.text.trim().isNotEmpty) {
          score = double.tryParse(_scoreController.text.trim());
        }
      }

      // Combine existing images with new images
      String? combinedImages;
      if (_existingImages.isNotEmpty || _selectedImages.isNotEmpty) {
        List<String> allImageNames = List.from(_existingImages);
        // Note: New images will be handled by the API and their names will be returned
        combinedImages = allImageNames.join(',');
      }

      final success = await provider.updateQuestionWithFiles(
        questionId: widget.question.id,
        question: _questionController.text.trim(),
        type: widget.question.type,
        imageFiles: _selectedImages.isNotEmpty ? _selectedImages : null,
        existingImages: combinedImages,
        answers: answers,
        correctAnswerIndex:
            widget.question.isPilihanGanda ? _correctAnswerIndex : null,
        answer: answer,
        score: score,
      );

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Pertanyaan berhasil diperbarui!'),
              backgroundColor: Color(0xFF455A9D),
            ),
          );
          Navigator.pop(context, true);
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                provider.errorMessage ?? 'Gagal memperbarui pertanyaan',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FD),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF333333)),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Edit ${_getQuestionTypeLabel(widget.question.type)}',
          style: const TextStyle(
            color: Color(0xFF333333),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: false,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Question Field
            const Text(
              'Pertanyaan*',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF333333),
              ),
            ),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: const Color(0xFFE0E0E0)),
              ),
              child: TextField(
                controller: _questionController,
                maxLines: 4,
                decoration: const InputDecoration(
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.all(16),
                  hintText: 'Masukkan pertanyaan',
                  hintStyle: TextStyle(color: Color(0xFF999999), fontSize: 16),
                ),
                style: const TextStyle(fontSize: 16, color: Color(0xFF333333)),
              ),
            ),
            const SizedBox(height: 20),

            // Images Section
            const Text(
              'Gambar (Opsional)',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF333333),
              ),
            ),
            const SizedBox(height: 8),

            // Add Image Button
            GestureDetector(
              onTap: _pickImages,
              child: Container(
                width: double.infinity,
                height: 120,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: const Color(0xFFE0E0E0),
                    style: BorderStyle.solid,
                  ),
                ),
                child: const Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.add_photo_alternate,
                      size: 48,
                      color: Color(0xFF999999),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Tap untuk menambah gambar',
                      style: TextStyle(color: Color(0xFF999999), fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),

            // Existing Images
            if (_existingImages.isNotEmpty) ...[
              const SizedBox(height: 12),
              const Text(
                'Gambar yang sudah ada:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF455A9D),
                ),
              ),
              const SizedBox(height: 8),
              SizedBox(
                height: 100,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: _existingImages.length,
                  itemBuilder: (context, index) {
                    return Container(
                      margin: const EdgeInsets.only(right: 12),
                      child: Stack(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: _buildExistingImageWidget(
                              _existingImages[index],
                            ),
                          ),
                          Positioned(
                            top: 4,
                            right: 4,
                            child: GestureDetector(
                              onTap: () => _removeExistingImage(index),
                              child: Container(
                                width: 24,
                                height: 24,
                                decoration: const BoxDecoration(
                                  color: Colors.red,
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.close,
                                  color: Colors.white,
                                  size: 16,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],

            // Selected Images
            if (_selectedImages.isNotEmpty) ...[
              const SizedBox(height: 12),
              const Text(
                'Gambar baru yang dipilih:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF455A9D),
                ),
              ),
              const SizedBox(height: 8),
              const SizedBox(height: 12),
              SizedBox(
                height: 100,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: _selectedImages.length,
                  itemBuilder: (context, index) {
                    return Container(
                      margin: const EdgeInsets.only(right: 12),
                      child: Stack(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: _buildImageWidget(_selectedImages[index]),
                          ),
                          Positioned(
                            top: 4,
                            right: 4,
                            child: GestureDetector(
                              onTap: () => _removeImage(index),
                              child: Container(
                                width: 24,
                                height: 24,
                                decoration: const BoxDecoration(
                                  color: Colors.red,
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.close,
                                  color: Colors.white,
                                  size: 16,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
            const SizedBox(height: 20),

            // Answers Section
            _buildAnswersSection(),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => Navigator.pop(context),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    side: const BorderSide(color: Color(0xFF455A9D)),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Batal',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF455A9D),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                flex: 2,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _updateQuestion,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF455A9D),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                  child:
                      _isLoading
                          ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                          : const Text(
                            'Simpan Perubahan',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnswersSection() {
    if (widget.question.isPilihanGanda) {
      return _buildPilihanGandaAnswers();
    } else {
      return _buildEssayAnswer();
    }
  }

  Widget _buildPilihanGandaAnswers() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Pilihan Jawaban*',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF333333),
          ),
        ),
        const SizedBox(height: 8),

        // Answer options
        for (int i = 0; i < _answerControllers.length; i++)
          Container(
            margin: const EdgeInsets.only(bottom: 12),
            child: Row(
              children: [
                // Radio button
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _correctAnswerIndex = i;
                    });
                  },
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color:
                            _correctAnswerIndex == i
                                ? const Color(0xFF455A9D)
                                : const Color(0xFFE0E0E0),
                        width: 2,
                      ),
                      color:
                          _correctAnswerIndex == i
                              ? const Color(0xFF455A9D)
                              : Colors.white,
                    ),
                    child:
                        _correctAnswerIndex == i
                            ? const Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 16,
                            )
                            : null,
                  ),
                ),
                const SizedBox(width: 12),

                // Answer text field
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: const Color(0xFFE0E0E0)),
                    ),
                    child: TextField(
                      controller: _answerControllers[i],
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.all(16),
                        hintText: 'Pilihan ${String.fromCharCode(65 + i)}',
                        hintStyle: const TextStyle(
                          color: Color(0xFF999999),
                          fontSize: 16,
                        ),
                      ),
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF333333),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildEssayAnswer() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Jawaban*',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF333333),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: const Color(0xFFE0E0E0)),
          ),
          child: TextField(
            controller: _answerControllers[0],
            maxLines: 4,
            decoration: const InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(16),
              hintText: 'Masukkan jawaban',
              hintStyle: TextStyle(color: Color(0xFF999999), fontSize: 16),
            ),
            style: const TextStyle(fontSize: 16, color: Color(0xFF333333)),
          ),
        ),
        // Add score input for essay questions
        if (widget.question.type == 'esai') ...[
          const SizedBox(height: 16),
          const Text(
            'Skor*',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF333333),
            ),
          ),
          const SizedBox(height: 8),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: const Color(0xFFE0E0E0)),
            ),
            child: TextField(
              controller: _scoreController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                border: InputBorder.none,
                contentPadding: EdgeInsets.all(16),
                hintText: 'Masukkan skor (contoh: 10)',
                hintStyle: TextStyle(color: Color(0xFF999999), fontSize: 16),
              ),
              style: const TextStyle(fontSize: 16, color: Color(0xFF333333)),
            ),
          ),
        ],
      ],
    );
  }

  String _getQuestionTypeLabel(String type) {
    switch (type) {
      case 'pilihan_ganda':
        return 'Pilihan Ganda';
      case 'uraian_singkat':
        return 'Uraian Singkat';
      case 'esai':
        return 'Essay';
      default:
        return type;
    }
  }
}
