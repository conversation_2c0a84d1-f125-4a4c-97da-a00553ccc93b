1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.examhots_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:11:5-67
15-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:11:22-64
16    <!-- Permissions for camera and file access -->
17    <uses-permission android:name="android.permission.CAMERA" />
17-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:3:5-65
17-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:3:22-62
18    <uses-permission
18-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:4:5-80
19        android:name="android.permission.READ_EXTERNAL_STORAGE"
19-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:4:22-77
20        android:maxSdkVersion="32" />
20-->[:photo_manager] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\photo_manager\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-35
21    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
21-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:5:5-81
21-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:5:22-78
22    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
22-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:6:5-76
22-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:6:22-73
23    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
23-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:7:5-75
23-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:7:22-72
24    <uses-permission
24-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:8:5-10:38
25        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
25-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:9:9-66
26        android:maxSdkVersion="32" />
26-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:10:9-35
27    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" /> <!-- Camera features -->
27-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:12:5-80
27-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:12:22-77
28    <uses-feature
28-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:15:5-17:36
29        android:name="android.hardware.camera"
29-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:16:9-47
30        android:required="false" />
30-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:17:9-33
31    <uses-feature
31-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:18:5-20:36
32        android:name="android.hardware.camera.autofocus"
32-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:19:9-57
33        android:required="false" />
33-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:20:9-33
34    <!--
35 Required to query activities that can process text, see:
36         https://developer.android.com/training/package-visibility and
37         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
38
39         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
40    -->
41    <queries>
41-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:58:5-63:15
42        <intent>
42-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:59:9-62:18
43            <action android:name="android.intent.action.PROCESS_TEXT" />
43-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:60:13-73
43-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:60:21-70
44
45            <data android:mimeType="text/plain" />
45-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:61:13-51
45-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:61:19-48
46        </intent>
47        <intent>
47-->[:file_picker] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
48            <action android:name="android.intent.action.GET_CONTENT" />
48-->[:file_picker] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
48-->[:file_picker] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
49
50            <data android:mimeType="*/*" />
50-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:61:13-51
50-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:61:19-48
51        </intent>
52    </queries>
53
54    <uses-permission android:name="android.permission.RECORD_AUDIO" />
54-->[:camera_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-71
54-->[:camera_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-68
55    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
55-->[androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff062a85859937196f6fab66cf837312\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:5-79
55-->[androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff062a85859937196f6fab66cf837312\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:22-76
56
57    <permission
57-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\270f809fe37752e6ee2e2f445df631dc\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
58        android:name="com.example.examhots_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
58-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\270f809fe37752e6ee2e2f445df631dc\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
59        android:protectionLevel="signature" />
59-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\270f809fe37752e6ee2e2f445df631dc\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
60
61    <uses-permission android:name="com.example.examhots_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
61-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\270f809fe37752e6ee2e2f445df631dc\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
61-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\270f809fe37752e6ee2e2f445df631dc\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
62
63    <application
64        android:name="android.app.Application"
65        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
65-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\270f809fe37752e6ee2e2f445df631dc\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
66        android:debuggable="true"
67        android:extractNativeLibs="true"
68        android:icon="@mipmap/launcher_icon"
69        android:label="Hots UM" >
70        <activity
71            android:name="com.example.examhots_app.MainActivity"
72            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
73            android:exported="true"
74            android:hardwareAccelerated="true"
75            android:launchMode="singleTop"
76            android:taskAffinity=""
77            android:theme="@style/LaunchTheme"
78            android:windowSoftInputMode="adjustResize" >
79
80            <!--
81                 Specifies an Android theme to apply to this Activity as soon as
82                 the Android process has started. This theme is visible to the user
83                 while the Flutter UI initializes. After that, this theme continues
84                 to determine the Window background behind the Flutter UI.
85            -->
86            <meta-data
87                android:name="io.flutter.embedding.android.NormalTheme"
88                android:resource="@style/NormalTheme" />
89
90            <intent-filter>
91                <action android:name="android.intent.action.MAIN" />
92
93                <category android:name="android.intent.category.LAUNCHER" />
94            </intent-filter>
95        </activity>
96        <!--
97             Don't delete the meta-data below.
98             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
99        -->
100        <meta-data
101            android:name="flutterEmbedding"
102            android:value="2" />
103
104        <provider
104-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
105            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
105-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
106            android:authorities="com.example.examhots_app.flutter.image_provider"
106-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
107            android:exported="false"
107-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
108            android:grantUriPermissions="true" >
108-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
109            <meta-data
109-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
110                android:name="android.support.FILE_PROVIDER_PATHS"
110-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
111                android:resource="@xml/flutter_image_picker_file_paths" />
111-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
112        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
113        <service
113-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
114            android:name="com.google.android.gms.metadata.ModuleDependencies"
114-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
115            android:enabled="false"
115-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
116            android:exported="false" >
116-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
117            <intent-filter>
117-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
118                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
118-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
118-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
119            </intent-filter>
120
121            <meta-data
121-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
122                android:name="photopicker_activity:0:required"
122-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
123                android:value="" />
123-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
124        </service>
125
126        <activity
126-->[:url_launcher_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
127            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
127-->[:url_launcher_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
128            android:exported="false"
128-->[:url_launcher_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
129            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
129-->[:url_launcher_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
130
131        <provider
131-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f30425c2f139e42314d7eb5f596106e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
132            android:name="androidx.startup.InitializationProvider"
132-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f30425c2f139e42314d7eb5f596106e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:25:13-67
133            android:authorities="com.example.examhots_app.androidx-startup"
133-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f30425c2f139e42314d7eb5f596106e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:26:13-68
134            android:exported="false" >
134-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f30425c2f139e42314d7eb5f596106e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:27:13-37
135            <meta-data
135-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f30425c2f139e42314d7eb5f596106e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
136                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
136-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f30425c2f139e42314d7eb5f596106e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
137                android:value="androidx.startup" />
137-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f30425c2f139e42314d7eb5f596106e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
138            <meta-data
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
139                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
140                android:value="androidx.startup" />
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
141        </provider>
142
143        <uses-library
143-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8a2eb46d474386dd22af3b353c4ac8d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
144            android:name="androidx.window.extensions"
144-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8a2eb46d474386dd22af3b353c4ac8d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
145            android:required="false" />
145-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8a2eb46d474386dd22af3b353c4ac8d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
146        <uses-library
146-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8a2eb46d474386dd22af3b353c4ac8d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
147            android:name="androidx.window.sidecar"
147-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8a2eb46d474386dd22af3b353c4ac8d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
148            android:required="false" />
148-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8a2eb46d474386dd22af3b353c4ac8d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
149
150        <receiver
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
151            android:name="androidx.profileinstaller.ProfileInstallReceiver"
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
152            android:directBootAware="false"
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
153            android:enabled="true"
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
154            android:exported="true"
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
155            android:permission="android.permission.DUMP" >
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
156            <intent-filter>
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
157                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
157-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
157-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
158            </intent-filter>
159            <intent-filter>
159-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
160                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
161            </intent-filter>
162            <intent-filter>
162-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
163                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
163-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
163-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
164            </intent-filter>
165            <intent-filter>
165-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
166                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
166-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
166-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
167            </intent-filter>
168        </receiver>
169    </application>
170
171</manifest>
