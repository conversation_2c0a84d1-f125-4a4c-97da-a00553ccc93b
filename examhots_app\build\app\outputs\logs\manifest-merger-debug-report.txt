-- Merging decision tree log ---
application
INJECTED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:22:5-52:19
INJECTED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\debug\AndroidManifest.xml
MERGED from [:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:url_launcher_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d657cd358054d317696ad5eacd2ed0c6\transformed\jetified-glide-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d657cd358054d317696ad5eacd2ed0c6\transformed\jetified-glide-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f30425c2f139e42314d7eb5f596106e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f30425c2f139e42314d7eb5f596106e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8a2eb46d474386dd22af3b353c4ac8d\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8a2eb46d474386dd22af3b353c4ac8d\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\270f809fe37752e6ee2e2f445df631dc\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\270f809fe37752e6ee2e2f445df631dc\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f23acbe6d7593afa55ee3593bdb54a02\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f23acbe6d7593afa55ee3593bdb54a02\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4baec83b051a3834a0eb3864ad3c5279\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4baec83b051a3834a0eb3864ad3c5279\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\feffeec679b9f2b670002599bc298c88\transformed\jetified-gifdecoder-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\feffeec679b9f2b670002599bc298c88\transformed\jetified-gifdecoder-4.15.1\AndroidManifest.xml:9:5-20
	android:extractNativeLibs
		INJECTED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\270f809fe37752e6ee2e2f445df631dc\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml
manifest
ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:1:1-64:12
MERGED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:1:1-64:12
INJECTED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:file_picker] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:photo_manager] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\photo_manager\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-11:12
MERGED from [:shared_preferences_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:sensors_plus] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\sensors_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-34:12
MERGED from [:url_launcher_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-14:12
MERGED from [:camera_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:flutter_plugin_android_lifecycle] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:path_provider_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:permission_handler_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:video_player_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\video_player_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d657cd358054d317696ad5eacd2ed0c6\transformed\jetified-glide-4.15.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2942a0c28d008797addcc9f3bb5826ce\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f6e129a58cc7512fb673e14c270417c\transformed\appcompat-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\920b23359abf906456db6802e9038da1\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa6de64488a6b5f463a1b82676df3dd3\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4aef8b0f11efc11fe8638426c9cff504\transformed\jetified-activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\335367fc0eb5d896d50531e6d49f3351\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\294bcf2280ed36d6f34ada894acc97e9\transformed\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ae4b1f592ad990ca80d18555016f1ae\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cfe665d7479b2a39d37f402312844abc\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\583f9f8ffc5031eff48d08479d774b1f\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e88cb1e0a48313dd9cb38e40bfad60cb\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a64f308c9c25b572ea388a104e29b0e\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbfd17e4ea66a71fc29e1e84225bdb07\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce237008317e7f109942f99d655adc35\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a6ad781135574fc648aaa0cd0ec03a5\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e32e85c174f204a1d85ae25c3fda2e3\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d17f177f18b56ea07168ee1561b973f\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13462a36353718b4886eb9461d549f81\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f30425c2f139e42314d7eb5f596106e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21cce336c1fc69645a3c1e9a94242a02\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7afecc99df61fd3e53e670e02645da2\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab794a1d4bffa082236177404f7fd745\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\787a8b14926951c07a3da5a84e85c930\transformed\jetified-core-ktx-1.15.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16720e7ca9bb6b2026c1d146ba2e922c\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3873b997379be0cba841f9627f89e40\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\875b6b6b1d12535ed23eafaffb496972\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\892acb0ddc1e9d80af642ec5562932ec\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96f2eb56c68341c6a69748f55bf44d63\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d90addbc542b549b27025a49cc090512\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff062a85859937196f6fab66cf837312\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69637c05f18bf99dacfc5443576bf530\transformed\jetified-media3-exoplayer-hls-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e7300eee7d0c48472a1bad57d53101c\transformed\jetified-media3-exoplayer-dash-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa1f9a45acd8245274e49b0dbc2a315\transformed\jetified-media3-exoplayer-rtsp-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2471f0ba73d42a257ebb0611a434fecd\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56602ee841d6acb4906164511874be56\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe201ed7331e8d167382a61901ed5a6b\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8a2eb46d474386dd22af3b353c4ac8d\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56ef6ec520469aef12513070e50346ef\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\210a5faa50ea0ae18f5e0fdf251b2d54\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09f96eeb635d39b14fa47085f1315aa5\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b09901525f74d4c7af1b675599032a7\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4472182d71694446fd01ee338b64147\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ed857e38831f88c62778a31b9925ded\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28e3dfa60d98f30b5ea1dd127dda1947\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fba1cb7934f1917d0d7056d6a5ebac\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6359b7e068d2c193a9eda5dcae4b0f1e\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e09361383f42a83d7f184f27d782b241\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60cc475df1d0d5b271c6bffb2305987d\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\270f809fe37752e6ee2e2f445df631dc\transformed\core-1.15.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fef096f04676664493f5925f33a2c852\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f23acbe6d7593afa55ee3593bdb54a02\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e54faa123f50295a5b95526929be0275\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bbe0012f979011780e8c980cfdd91fc\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4baec83b051a3834a0eb3864ad3c5279\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\feffeec679b9f2b670002599bc298c88\transformed\jetified-gifdecoder-4.15.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90a1937b6787bfe9102c8195df335e16\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\51b8176d586d97247e65f03861b3b855\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e000a7408fed2a7c36eab61bf21bdc7\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\341e7fe931594b5eb449233e44baa721\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7cb7f9a938d3d5561dd08a0f8385eda\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\199955966574c86288cb37283ac218be\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdb52904c667d6f19fab028f2c1a99cb\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8011f59056f3bd1001db9958274d623d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1b4aa65c4d252075ab66d637ce37eb\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f345ea5c60915d336594899608fac7d6\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72eebdda077f3f60a02869f60896d1f1\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.CAMERA
ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:3:5-65
MERGED from [:camera_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-65
MERGED from [:camera_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-65
	android:name
		ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:3:22-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:4:5-80
MERGED from [:photo_manager] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\photo_manager\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-9:38
MERGED from [:photo_manager] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\photo_manager\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-9:38
	android:maxSdkVersion
		ADDED from [:photo_manager] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\photo_manager\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-35
	android:name
		ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:4:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:5:5-81
	android:name
		ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:5:22-78
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:6:5-76
	android:name
		ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:6:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:7:5-75
	android:name
		ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:7:22-72
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:8:5-10:38
	android:maxSdkVersion
		ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:10:9-35
	android:name
		ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:9:9-66
uses-permission#android.permission.INTERNET
ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:11:5-67
MERGED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:11:5-67
MERGED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:11:5-67
	android:name
		ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:11:22-64
uses-permission#android.permission.ACCESS_MEDIA_LOCATION
ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:12:5-80
	android:name
		ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:12:22-77
uses-feature#android.hardware.camera
ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:15:5-17:36
	android:required
		ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:17:9-33
	android:name
		ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:16:9-47
uses-feature#android.hardware.camera.autofocus
ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:18:5-20:36
	android:required
		ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:20:9-33
	android:name
		ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:19:9-57
queries
ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:58:5-63:15
MERGED from [:file_picker] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:15
MERGED from [:file_picker] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:59:9-62:18
action#android.intent.action.PROCESS_TEXT
ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:60:13-73
	android:name
		ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:60:21-70
data
ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:61:13-51
	android:mimeType
		ADDED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:61:19-48
uses-sdk
INJECTED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\debug\AndroidManifest.xml
MERGED from [:file_picker] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:file_picker] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:photo_manager] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\photo_manager\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:photo_manager] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\photo_manager\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sensors_plus] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\sensors_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sensors_plus] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\sensors_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:url_launcher_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:camera_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:camera_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:video_player_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\video_player_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:video_player_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\video_player_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d657cd358054d317696ad5eacd2ed0c6\transformed\jetified-glide-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d657cd358054d317696ad5eacd2ed0c6\transformed\jetified-glide-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2942a0c28d008797addcc9f3bb5826ce\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2942a0c28d008797addcc9f3bb5826ce\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f6e129a58cc7512fb673e14c270417c\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f6e129a58cc7512fb673e14c270417c\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\920b23359abf906456db6802e9038da1\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\920b23359abf906456db6802e9038da1\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa6de64488a6b5f463a1b82676df3dd3\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa6de64488a6b5f463a1b82676df3dd3\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4aef8b0f11efc11fe8638426c9cff504\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4aef8b0f11efc11fe8638426c9cff504\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\335367fc0eb5d896d50531e6d49f3351\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\335367fc0eb5d896d50531e6d49f3351\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\294bcf2280ed36d6f34ada894acc97e9\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\294bcf2280ed36d6f34ada894acc97e9\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ae4b1f592ad990ca80d18555016f1ae\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ae4b1f592ad990ca80d18555016f1ae\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cfe665d7479b2a39d37f402312844abc\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cfe665d7479b2a39d37f402312844abc\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\583f9f8ffc5031eff48d08479d774b1f\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\583f9f8ffc5031eff48d08479d774b1f\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e88cb1e0a48313dd9cb38e40bfad60cb\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e88cb1e0a48313dd9cb38e40bfad60cb\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a64f308c9c25b572ea388a104e29b0e\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a64f308c9c25b572ea388a104e29b0e\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbfd17e4ea66a71fc29e1e84225bdb07\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbfd17e4ea66a71fc29e1e84225bdb07\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce237008317e7f109942f99d655adc35\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce237008317e7f109942f99d655adc35\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a6ad781135574fc648aaa0cd0ec03a5\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a6ad781135574fc648aaa0cd0ec03a5\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e32e85c174f204a1d85ae25c3fda2e3\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e32e85c174f204a1d85ae25c3fda2e3\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d17f177f18b56ea07168ee1561b973f\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d17f177f18b56ea07168ee1561b973f\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13462a36353718b4886eb9461d549f81\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13462a36353718b4886eb9461d549f81\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f30425c2f139e42314d7eb5f596106e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f30425c2f139e42314d7eb5f596106e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21cce336c1fc69645a3c1e9a94242a02\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\21cce336c1fc69645a3c1e9a94242a02\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7afecc99df61fd3e53e670e02645da2\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7afecc99df61fd3e53e670e02645da2\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab794a1d4bffa082236177404f7fd745\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab794a1d4bffa082236177404f7fd745\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\787a8b14926951c07a3da5a84e85c930\transformed\jetified-core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\787a8b14926951c07a3da5a84e85c930\transformed\jetified-core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16720e7ca9bb6b2026c1d146ba2e922c\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16720e7ca9bb6b2026c1d146ba2e922c\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3873b997379be0cba841f9627f89e40\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3873b997379be0cba841f9627f89e40\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\875b6b6b1d12535ed23eafaffb496972\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\875b6b6b1d12535ed23eafaffb496972\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\892acb0ddc1e9d80af642ec5562932ec\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\892acb0ddc1e9d80af642ec5562932ec\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96f2eb56c68341c6a69748f55bf44d63\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96f2eb56c68341c6a69748f55bf44d63\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d90addbc542b549b27025a49cc090512\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d90addbc542b549b27025a49cc090512\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff062a85859937196f6fab66cf837312\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff062a85859937196f6fab66cf837312\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69637c05f18bf99dacfc5443576bf530\transformed\jetified-media3-exoplayer-hls-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69637c05f18bf99dacfc5443576bf530\transformed\jetified-media3-exoplayer-hls-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e7300eee7d0c48472a1bad57d53101c\transformed\jetified-media3-exoplayer-dash-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e7300eee7d0c48472a1bad57d53101c\transformed\jetified-media3-exoplayer-dash-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa1f9a45acd8245274e49b0dbc2a315\transformed\jetified-media3-exoplayer-rtsp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aa1f9a45acd8245274e49b0dbc2a315\transformed\jetified-media3-exoplayer-rtsp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2471f0ba73d42a257ebb0611a434fecd\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2471f0ba73d42a257ebb0611a434fecd\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56602ee841d6acb4906164511874be56\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56602ee841d6acb4906164511874be56\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe201ed7331e8d167382a61901ed5a6b\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe201ed7331e8d167382a61901ed5a6b\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8a2eb46d474386dd22af3b353c4ac8d\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8a2eb46d474386dd22af3b353c4ac8d\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56ef6ec520469aef12513070e50346ef\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56ef6ec520469aef12513070e50346ef\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\210a5faa50ea0ae18f5e0fdf251b2d54\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\210a5faa50ea0ae18f5e0fdf251b2d54\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09f96eeb635d39b14fa47085f1315aa5\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09f96eeb635d39b14fa47085f1315aa5\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b09901525f74d4c7af1b675599032a7\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b09901525f74d4c7af1b675599032a7\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4472182d71694446fd01ee338b64147\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4472182d71694446fd01ee338b64147\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ed857e38831f88c62778a31b9925ded\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ed857e38831f88c62778a31b9925ded\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28e3dfa60d98f30b5ea1dd127dda1947\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28e3dfa60d98f30b5ea1dd127dda1947\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fba1cb7934f1917d0d7056d6a5ebac\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fba1cb7934f1917d0d7056d6a5ebac\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6359b7e068d2c193a9eda5dcae4b0f1e\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6359b7e068d2c193a9eda5dcae4b0f1e\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e09361383f42a83d7f184f27d782b241\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e09361383f42a83d7f184f27d782b241\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60cc475df1d0d5b271c6bffb2305987d\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60cc475df1d0d5b271c6bffb2305987d\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\270f809fe37752e6ee2e2f445df631dc\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\270f809fe37752e6ee2e2f445df631dc\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fef096f04676664493f5925f33a2c852\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fef096f04676664493f5925f33a2c852\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f23acbe6d7593afa55ee3593bdb54a02\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f23acbe6d7593afa55ee3593bdb54a02\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e54faa123f50295a5b95526929be0275\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e54faa123f50295a5b95526929be0275\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bbe0012f979011780e8c980cfdd91fc\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bbe0012f979011780e8c980cfdd91fc\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4baec83b051a3834a0eb3864ad3c5279\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4baec83b051a3834a0eb3864ad3c5279\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\feffeec679b9f2b670002599bc298c88\transformed\jetified-gifdecoder-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\feffeec679b9f2b670002599bc298c88\transformed\jetified-gifdecoder-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90a1937b6787bfe9102c8195df335e16\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90a1937b6787bfe9102c8195df335e16\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\51b8176d586d97247e65f03861b3b855\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\51b8176d586d97247e65f03861b3b855\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e000a7408fed2a7c36eab61bf21bdc7\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e000a7408fed2a7c36eab61bf21bdc7\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\341e7fe931594b5eb449233e44baa721\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\341e7fe931594b5eb449233e44baa721\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7cb7f9a938d3d5561dd08a0f8385eda\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7cb7f9a938d3d5561dd08a0f8385eda\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\199955966574c86288cb37283ac218be\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\199955966574c86288cb37283ac218be\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdb52904c667d6f19fab028f2c1a99cb\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cdb52904c667d6f19fab028f2c1a99cb\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8011f59056f3bd1001db9958274d623d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8011f59056f3bd1001db9958274d623d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1b4aa65c4d252075ab66d637ce37eb\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1b4aa65c4d252075ab66d637ce37eb\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f345ea5c60915d336594899608fac7d6\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f345ea5c60915d336594899608fac7d6\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72eebdda077f3f60a02869f60896d1f1\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72eebdda077f3f60a02869f60896d1f1\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
	android:targetSdkVersion
		INJECTED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\debug\AndroidManifest.xml
intent#action:name:android.intent.action.GET_CONTENT+data:mimeType:*/*
ADDED from [:file_picker] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
action#android.intent.action.GET_CONTENT
ADDED from [:file_picker] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
	android:name
		ADDED from [:file_picker] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
	android:resource
		ADDED from [:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
	android:name
		ADDED from [:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
uses-permission#android.permission.RECORD_AUDIO
ADDED from [:camera_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-71
	android:name
		ADDED from [:camera_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-68
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f30425c2f139e42314d7eb5f596106e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f23acbe6d7593afa55ee3593bdb54a02\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f23acbe6d7593afa55ee3593bdb54a02\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f30425c2f139e42314d7eb5f596106e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f30425c2f139e42314d7eb5f596106e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f30425c2f139e42314d7eb5f596106e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f30425c2f139e42314d7eb5f596106e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f30425c2f139e42314d7eb5f596106e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f30425c2f139e42314d7eb5f596106e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f30425c2f139e42314d7eb5f596106e\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff062a85859937196f6fab66cf837312\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56602ee841d6acb4906164511874be56\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56602ee841d6acb4906164511874be56\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff062a85859937196f6fab66cf837312\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:22-76
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8a2eb46d474386dd22af3b353c4ac8d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8a2eb46d474386dd22af3b353c4ac8d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8a2eb46d474386dd22af3b353c4ac8d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8a2eb46d474386dd22af3b353c4ac8d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8a2eb46d474386dd22af3b353c4ac8d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8a2eb46d474386dd22af3b353c4ac8d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\270f809fe37752e6ee2e2f445df631dc\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\270f809fe37752e6ee2e2f445df631dc\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\270f809fe37752e6ee2e2f445df631dc\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
permission#com.example.examhots_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\270f809fe37752e6ee2e2f445df631dc\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\270f809fe37752e6ee2e2f445df631dc\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\270f809fe37752e6ee2e2f445df631dc\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\270f809fe37752e6ee2e2f445df631dc\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\270f809fe37752e6ee2e2f445df631dc\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.examhots_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\270f809fe37752e6ee2e2f445df631dc\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\270f809fe37752e6ee2e2f445df631dc\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd975a3f8547037975ce342cb8f27f5\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
