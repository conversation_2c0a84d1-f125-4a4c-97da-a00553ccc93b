import 'package:flutter/foundation.dart';
import '../models/user.dart';
import '../services/api_service.dart';

class AuthProvider with ChangeNotifier {
  User? _user;
  bool _isLoading = false;
  String? _errorMessage;

  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _user != null;

  // Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Set error message
  void _setError(String error) {
    _errorMessage = error;
    _setLoading(false);
  }

  // Login
  Future<bool> login(
    String email,
    String password, {
    bool rememberMe = false,
  }) async {
    try {
      _setLoading(true);
      clearError();

      final response = await ApiService.login(email, password);

      if (response['success'] == true) {
        _user = User.fromJson(response['data']['user']);

        // Save login credentials if remember me is checked
        if (rememberMe) {
          await ApiService.saveLoginCredentials(email, password);
        } else {
          await ApiService.clearLoginCredentials();
        }

        _setLoading(false);
        return true;
      } else {
        _setError(response['message'] ?? 'Login failed');
        return false;
      }
    } catch (e) {
      _setError(e.toString().replaceAll('Exception: ', ''));
      return false;
    }
  }

  // Logout
  Future<void> logout() async {
    try {
      _setLoading(true);
      await ApiService.logout();
    } catch (e) {
      // Even if logout fails on server, we still clear local data
      debugPrint('Logout error: $e');
    } finally {
      _user = null;
      _setLoading(false);
    }
  }

  // Load user profile
  Future<void> loadUserProfile() async {
    try {
      _setLoading(true);
      clearError();

      final response = await ApiService.getProfile();

      if (response['success'] == true) {
        _user = User.fromJson(response['data']['user']);
      } else {
        _setError(response['message'] ?? 'Failed to load profile');
      }
    } catch (e) {
      _setError(e.toString().replaceAll('Exception: ', ''));
    } finally {
      _setLoading(false);
    }
  }

  // Check authentication status
  Future<void> checkAuthStatus() async {
    try {
      _setLoading(true);

      final isAuth = await ApiService.isAuthenticated();

      if (isAuth) {
        await loadUserProfile();
      } else {
        _user = null;
      }
    } catch (e) {
      _user = null;
      debugPrint('Auth check error: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Update user profile
  Future<bool> updateProfile({
    String? nip,
    required String name,
    required String email,
    required String gender,
    required String phonenumber,
    String? address,
  }) async {
    try {
      _setLoading(true);
      clearError();

      final response = await ApiService.updateProfile(
        nip: nip,
        name: name,
        email: email,
        gender: gender,
        phonenumber: phonenumber,
        address: address,
      );

      if (response['success'] == true) {
        // Update local user data
        _user = User.fromJson(response['data']['user']);
        notifyListeners();
        return true;
      } else {
        _setError(response['message'] ?? 'Failed to update profile');
        return false;
      }
    } catch (e) {
      _setError(e.toString().replaceAll('Exception: ', ''));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Change user password
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
    required String newPasswordConfirmation,
  }) async {
    try {
      _setLoading(true);
      clearError();

      final response = await ApiService.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
        newPasswordConfirmation: newPasswordConfirmation,
      );

      if (response['success'] == true) {
        return true;
      } else {
        _setError(response['message'] ?? 'Failed to change password');
        return false;
      }
    } catch (e) {
      _setError(e.toString().replaceAll('Exception: ', ''));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Refresh user profile
  Future<void> refreshProfile() async {
    await loadUserProfile();
  }
}
